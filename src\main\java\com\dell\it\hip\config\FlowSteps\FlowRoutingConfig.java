package com.dell.it.hip.config.FlowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;
@Data
public class FlowRoutingConfig implements RuleEnabledStepConfig {
    private boolean dbBacked;

    @Override
    public List<RuleRef> getRuleRefs() {
        return null;
    }
    public boolean isDbBacked() { return dbBacked; }
    public void setDbBacked(boolean dbBacked) { this.dbBacked = dbBacked;}
}