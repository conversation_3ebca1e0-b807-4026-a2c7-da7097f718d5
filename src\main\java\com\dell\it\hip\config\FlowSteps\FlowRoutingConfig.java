package com.dell.it.hip.config.FlowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;
@Data
public class FlowRoutingConfig implements RuleEnabledStepConfig {
    private List<RuleRef> ruleRefs;
    // ...other config properties...
   // public List<RuleRef> getRules() { return rules; }
    public void setRuleRefs(List<RuleRef> ruleRefs) { this.ruleRefs = ruleRefs; }

    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }
}