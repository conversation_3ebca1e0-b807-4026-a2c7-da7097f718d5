package com.dell.it.hip.core.registry;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.dell.it.hip.strategy.flows.ContivoTransformer;

public class ContivoTransformerRegistry {
	
    private final Map<String, ContivoTransformer> registry = new ConcurrentHashMap<>();

    public void register(String name, ContivoTransformer transformer) {
        registry.put(name, transformer);
    }

    public ContivoTransformer get(String name) {
        ContivoTransformer transformer = registry.get(name);
        if (transformer == null)
            throw new IllegalArgumentException("Transformer not found: " + name);
        return transformer;
    }
}