package com.dell.it.hip.config.FlowSteps;



import java.util.List;
import java.util.Map;

public class DocTypeProcessorStepConfig extends FlowStepConfig {
    // For each format (JSON, XML, etc), which docTypes to check (in order)
    private Map<String, List<String>> supportedDocTypesPerFormat;
    private String genericDocType; // e.g. "GENERIC"

    public Map<String, List<String>> getSupportedDocTypesPerFormat() {
        return supportedDocTypesPerFormat;
    }

    public void setSupportedDocTypesPerFormat(Map<String, List<String>> supportedDocTypesPerFormat) {
        this.supportedDocTypesPerFormat = supportedDocTypesPerFormat;
    }

    public String getGenericDocType() {
        return genericDocType;
    }

    public void setGenericDocType(String genericDocType) {
        this.genericDocType = genericDocType;
    }

    public boolean isAllowGenericDocType() {
        return allowGenericDocType;
    }

    public void setAllowGenericDocType(boolean allowGenericDocType) {
        this.allowGenericDocType = allowGenericDocType;
    }

    public boolean isTerminateOnUnknownFormat() {
        return terminateOnUnknownFormat;
    }

    public void setTerminateOnUnknownFormat(boolean terminateOnUnknownFormat) {
        this.terminateOnUnknownFormat = terminateOnUnknownFormat;
    }

    public Map<String, AttributeMapping> getAttributeMappings() {
        return attributeMappings;
    }

    public void setAttributeMappings(Map<String, AttributeMapping> attributeMappings) {
        this.attributeMappings = attributeMappings;
    }

    public ValidationConfig getValidation() {
        return validation;
    }

    public void setValidation(ValidationConfig validation) {
        this.validation = validation;
    }

    private boolean allowGenericDocType;
    private boolean terminateOnUnknownFormat;
    private Map<String, AttributeMapping> attributeMappings; // Extra, optional
    private ValidationConfig validation; // Fallback for generic

    // Getters & Setters (Lombok can be used in your project)
    // ...
}