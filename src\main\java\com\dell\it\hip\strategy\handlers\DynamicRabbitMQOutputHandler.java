package com.dell.it.hip.strategy.handlers;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;

import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicRabbitMQHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.HeaderFilterUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

@Component("rabbitMQHandler")
public class DynamicRabbitMQOutputHandler extends AbstractOutputHandlerStrategy {

    private final Map<String, Channel> channelMap = new ConcurrentHashMap<>();
    private final Map<String, Connection> connectionMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();
    private final Set<String> headersToFilter;

    public DynamicRabbitMQOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "rabbitMQHandler";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicRabbitMQHandlerConfig config = def.getConfig(ref.getId(), DynamicRabbitMQHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicRabbitMQHandlerConfig not found for ref: " + ref.getId());

        String exchange = config.getExchange();
        String routingKey = config.getRoutingKey();
        if (exchange == null || routingKey == null)
            throw new IllegalArgumentException("RabbitMQ exchange or routingKey not configured");

        boolean useGzip = Boolean.TRUE.equals(config.getGzipEnabled());

        byte[] payload = useGzip ? CompressionUtil.compress(message.getPayload()) : convertToBytes(message.getPayload());
        Map<String, Object> filteredHeaders = HeaderFilterUtil.filterHeaders(message.getHeaders(), headersToFilter);

        Channel channel = getOrCreateChannel(config, ref);

        // Build properties
        AMQP.BasicProperties.Builder propsBuilder = new AMQP.BasicProperties.Builder();
        if (Boolean.TRUE.equals(config.getPersistent())) {
            propsBuilder.deliveryMode(2); // persistent
        } else {
            propsBuilder.deliveryMode(1); // non-persistent
        }
        // Add headers
        if (!filteredHeaders.isEmpty()) {
            propsBuilder.headers(filteredHeaders);
        }
        AMQP.BasicProperties props = propsBuilder.build();

        try {
            channel.basicPublish(
                    exchange,
                    routingKey,
                    Boolean.TRUE.equals(config.getMandatory()),
                    props,
                    payload
            );
            wiretapService.tap(message, def, ref, "completed", "Message sent to RabbitMQ: exchange=" + exchange + ", routingKey=" + routingKey);
        } catch (IOException ex) {
            wiretapService.tap(message, def, ref, "error", "RabbitMQ send failed: " + ex.getMessage());
            throw ex;
        }
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    private Channel getOrCreateChannel(DynamicRabbitMQHandlerConfig config, HandlerConfigRef ref) throws IOException, TimeoutException {
        String key = ref.getId();
        Channel channel = channelMap.get(key);
        if (channel == null || !channel.isOpen()) {
            Connection conn = connectionMap.get(key);
            if (conn == null || !conn.isOpen()) {
                ConnectionFactory factory = new ConnectionFactory();
                factory.setHost(config.getHost());
                if (config.getPort() != null) factory.setPort(config.getPort());
                if (config.getUsername() != null) factory.setUsername(config.getUsername());
                if (config.getPassword() != null) factory.setPassword(config.getPassword());
                if (config.getVirtualHost() != null) factory.setVirtualHost(config.getVirtualHost());
                // SSL, timeouts, tuning from parameters if present
                if (config.getParameters() != null) {
                    Object ssl = config.getParameters().get("sslEnabled");
                    if (Boolean.TRUE.equals(ssl)) {
                        try { factory.useSslProtocol(); } catch (Exception ignored) {}
                    }
                    // more params...
                }
                conn = factory.newConnection();
                connectionMap.put(key, conn);
            }
            channel = conn.createChannel();
            channelMap.put(key, channel);
        }
        return channel;
    }

    // --- LIFECYCLE methods ---
    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getId(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = ref.getId();
        Channel channel = channelMap.remove(key);
        if (channel != null) try { channel.close(); } catch (Exception ignored) {}
        Connection conn = connectionMap.remove(key);
        if (conn != null) try { conn.close(); } catch (Exception ignored) {}
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        channelMap.values().forEach(ch -> { try { ch.close(); } catch (Exception ignored) {} });
        channelMap.clear();
        connectionMap.values().forEach(conn -> { try { conn.close(); } catch (Exception ignored) {} });
        connectionMap.clear();
        handlerPauseState.clear();
        super.dispose();
    }
}