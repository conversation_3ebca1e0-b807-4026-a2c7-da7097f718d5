package com.dell.it.hip.util.redis;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.data.redis.core.StringRedisTemplate;

import com.dell.it.hip.config.FlowSteps.ContivoMap;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;

public class ContivoMapCache {
    private final StringRedisTemplate redisTemplate;
    private final Map<String, ContivoMap> cache = new ConcurrentHashMap<>();

    public ContivoMapCache(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public ContivoMap getOrLoad(String mapIdentifier, String mapIdentifierVersion) {
        String compositeKey = mapIdentifier + ":" + mapIdentifierVersion;
        return cache.computeIfAbsent(compositeKey, key -> {
            String json = redisTemplate.opsForValue().get(key);
            if (json == null)
                throw new RuntimeException("ContivoMap not found in Redis for key: " + key);
            return JsonUtil.fromJson(json, ContivoMap.class);
        });
    }
}
