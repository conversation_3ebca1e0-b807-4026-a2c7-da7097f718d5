package com.dell.it.hip.strategy.flows;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AttributeProcessorConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.dataformatUtils.CsvUtil;
import com.dell.it.hip.util.dataformatUtils.FlatFileUtil;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;
import com.dell.it.hip.util.dataformatUtils.RegexUtil;
import com.dell.it.hip.util.dataformatUtils.StaediUtil;
import com.dell.it.hip.util.dataformatUtils.XmlUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;

@Component("attributeProcessor")
public class AttributeProcessorFlowStepStrategy extends AbstractFlowStepStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AttributeProcessorFlowStepStrategy.class);


    @Override
    public String getType() {
        return "attributeProcessor";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef ref,
            HIPIntegrationDefinition def
    ) throws Exception {
        AttributeProcessorConfig config = (AttributeProcessorConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (config == null) {
            String error = "AttributeProcessorConfig missing for ref: " + ref.getPropertyRef();
            logger.error(error);
            throw new IllegalStateException(error);
        }

        String msgFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));
        Map<String, Object> enrichedHeaders = new HashMap<>();
        boolean errorFound = false;

        for (AttributeProcessorConfig.AttributeMapping mapping : config.getAttributeMappings()) {
            String value = null;

            AttributeProcessorConfig.DerivedFromType derivedFrom = mapping.getDerivedFrom();
            if (derivedFrom == null) continue;

            switch (derivedFrom) {
                case FILENAME:
                    value = (String) message.getHeaders().get("fileName");
                    if (mapping.getExpression() != null && value != null) {
                        value = RegexUtil.extract(value, mapping.getExpression());
                    }
                    break;
                case ROOT_ELEMENT:
                    if ("xml".equalsIgnoreCase(msgFormat)) {
                        value = XmlUtil.getRootElement(String.valueOf(message.getPayload()));
                    } else if ("json".equalsIgnoreCase(msgFormat)) {
                        value = JsonUtil.getRootElement(String.valueOf(message.getPayload()));
                    }
                    break;
                case DERIVED_FROM_PAYLOAD:
                    if ("xml".equalsIgnoreCase(msgFormat)) {
                        value = XmlUtil.extractFieldFlexible(String.valueOf(message.getPayload()), mapping.getExpression(), null);
                    } else if ("json".equalsIgnoreCase(msgFormat)) {
                        value = JsonUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("edi-x12".equalsIgnoreCase(msgFormat)) {
                        value = StaediUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("csv".equalsIgnoreCase(msgFormat)) {
                        value = CsvUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("flat_file".equalsIgnoreCase(msgFormat)) {
                        value = FlatFileUtil.extractFromPayload(String.valueOf(message.getPayload()), mapping.getExpression());
                    }
                    break;
                default:
                    logger.warn("Unknown DerivedFromType: {}", derivedFrom);
            }

            // --- Required attribute check ---
            if (value == null && mapping.isRequired()) {
                String err = String.format("Missing required attribute '%s' for mapping '%s'", mapping.getAttributeName(), mapping);
                logger.error(err);
                errorFound = true;
                // Optionally continue and collect all missing attributes, or break
                break;
            }

            // Handle usedIn - add as many headers as usedIn entries (prefix: hip.<usedInString>.attributeName)
            if (!CollectionUtils.isEmpty(mapping.getUsage())) {
                for (String usedIn : mapping.getUsage()) {
                    String key = "hip." + usedIn + "." + mapping.getAttributeName();
                    enrichedHeaders.put(key, value);
                }
            } else {
                // Default to just attribute name if no usedIn specified
                enrichedHeaders.put(mapping.getAttributeName(), value);
            }
        }

        if (errorFound) {
       //TODO:fix transactionlogging
            //    transactionLoggingUtil.logError( "AttributeProcessorFlowStepStrategy: required attribute missing", def, ref, message          );
            wiretapService.tap(
                    message, def, ref, "error", "Required attribute missing in attributeProcessor"
            );
            return Collections.emptyList();
        }

        Message<?> enriched = MessageBuilder
                .fromMessage(message)
                .copyHeaders(enrichedHeaders)
                .setHeader("HIP.messageFormat", msgFormat)
                .build();
        //TODO:fix transactionlogging
        //transactionLoggingUtil.logInfo("AttributeProcessorFlowStepStrategy executed", def, ref, enriched);

        return Collections.singletonList(enriched);
    }
}