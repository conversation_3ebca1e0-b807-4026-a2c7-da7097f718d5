package com.dell.it.hip.config.adapters;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicIBMMQAdapterConfig extends AdapterConfig {
    @JsonProperty("ibmmq.consumer.queueManager")
    private String queueManager;
    
    @JsonProperty("ibmmq.consumer.queue")
    private String queueName;
    
    @JsonProperty("ibmmq.consumer.channel")
    private String channel;
    
    @JsonProperty("ibmmq.consumer.connName")
    private String connName;
    
    @JsonProperty("ibmmq.consumer.auth.type")
    private String authenticationType;
    
    @JsonProperty("ibmmq.consumer.username")
    private String username;
    private String password;

    // SSL/TLS
    @JsonProperty("ibmmq.consumer.sslCipherSuite")
    private String sslCipherSuite;
    private String sslPeerName;
    private String sslKeystore;
    private String sslKeystorePassword;
    private String sslTruststore;
    private String sslTruststorePassword;

    // Charset/encoding
    private Integer ccsid;      // MQ CCSID
    private Integer encoding;   // MQ encoding
    private boolean compressed = false;

    // Performance/pooling/concurrency
    private Integer connectionPoolSize;
    private Integer concurrency;
    private Integer sessionCacheSize;
    private Long receiveTimeout;
    private Long recoveryInterval;
    private Boolean transacted;

    // Consumer
    private Boolean autoAck;
    private String messageSelector;
    private List<String> headersToExtract;

    // Advanced
    private Map<String, ?> properties;
    private String deadLetterQueueName;
    private Integer maxRedeliveryAttempts;
    private String messageConverterClass;

    // Override parent's properties method to provide specific type
    @Override
    public void setProperties(Map<String, ?> properties) {
        super.setProperties(properties);
    }

    // Convenience method for String-specific properties
    public void setStringProperties(Map<String, String> properties) {
        super.setProperties(properties);
    }

    // Getters and setters for all above...

    // ... (omitted for brevity)
}