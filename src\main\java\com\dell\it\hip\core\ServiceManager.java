package com.dell.it.hip.core;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.integration.support.context.NamedComponent;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;

/**
 * Central registry for all running HIP integrations, strategies, and topologies.
 */
@Service
public class ServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(ServiceManager.class);

    // Map: integrationKey ("name:version") -> HIPIntegrationDefinition
    private final Map<String, HIPIntegrationDefinition> definitionMap = new ConcurrentHashMap<>();

    // Map: integrationKey -> FlowTopology (channels, etc.)
    private final Map<String, FlowTopology> topologyMap = new ConcurrentHashMap<>();

    @Autowired
    private Map<String, InputAdapterStrategy> inputAdapterStrategyMap = new HashMap<>();
    @Autowired
    private Map<String, FlowStepStrategy> flowStepStrategyMap = new HashMap<>();
    @Autowired
    private Map<String, HandlerStrategy> handlerStrategyMap = new HashMap<>();

    @Autowired
    private TaskExecutor flowExecutor;

    // --- Registration and Lookup ---

    public void registerIntegration(HIPIntegrationDefinition def, List<MessageChannel> channels) {
        String key = integrationKey(def.getHipIntegrationName(), def.getVersion());
        definitionMap.put(key, def);
        topologyMap.put(key, new FlowTopology(def, channels, channels.isEmpty() ? null : channels.get(channels.size() - 1)));
        logger.info("ServiceManager: Registered integration: {}", key);
    }

    public void unregisterIntegration(String name, String version) {
        String key = integrationKey(name, version);
        definitionMap.remove(key);
        topologyMap.remove(key);
        logger.info("ServiceManager: Unregistered integration: {}", key);
    }

    public HIPIntegrationDefinition getIntegrationDefinition(String name, String version) {
        return definitionMap.get(integrationKey(name, version));
    }

    public List<HIPIntegrationDefinition> getAllDefinitions() {
        return new ArrayList<>(definitionMap.values());
    }

    public FlowTopology getTopology(String name, String version) {
        return topologyMap.get(integrationKey(name, version));
    }

    /**
     * Returns the first input channel for this integration (backward compatible).
     */
    public MessageChannel getInputChannel(String name, String version) {
        FlowTopology topo = topologyMap.get(integrationKey(name, version));
        return (topo != null && !topo.getChannels().isEmpty()) ? topo.getChannels().get(0) : null;
    }

    /**
     * Returns the input channel for a specific adapter in the integration, if present.
     * The orchestration should set the channel's component name as:
     *    integrationName.version.inputChannel.adapterRef
     */
    public MessageChannel getInputChannelByAdapterRef(String name, String version, String adapterRef) {
        FlowTopology topo = topologyMap.get(integrationKey(name, version));
        if (topo == null) return null;
        String expectedName = name + "." + version + ".inputChannel." + adapterRef;
        for (MessageChannel ch : topo.getChannels()) {
            if (ch instanceof NamedComponent named && expectedName.equals(named.getComponentName())) {
                return ch;
            }
        }
        // Fallback: if only one input channel, return it
        List<MessageChannel> channels = topo.getChannels();
        if (!channels.isEmpty()) {
            return channels.get(0);
        }
        return null;
    }

    public List<MessageChannel> getChannels(String name, String version) {
        FlowTopology topo = topologyMap.get(integrationKey(name, version));
        return topo != null ? topo.getChannels() : Collections.emptyList();
    }

    public MessageChannel getOutputChannel(String name, String version) {
        FlowTopology topo = topologyMap.get(integrationKey(name, version));
        return topo != null ? topo.getOutputChannel() : null;
    }

    // --- Adapter/handler/flowStep strategies ---

    public InputAdapterStrategy getInputAdapterStrategy(String type) {
        return inputAdapterStrategyMap.get(type);
    }

    public Map<String, InputAdapterStrategy> getInputAdapterStrategyMap() {
        return inputAdapterStrategyMap;
    }

    public HandlerStrategy getHandlerStrategy(String type) {
        return handlerStrategyMap.get(type);
    }

    public Map<String, HandlerStrategy> getHandlerStrategyMap() {
        return handlerStrategyMap;
    }

    public FlowStepStrategy getFlowStepStrategy(String type) {
        return flowStepStrategyMap.get(type);
    }

    public Map<String, FlowStepStrategy> getFlowStepStrategyMap() {
        return flowStepStrategyMap;
    }

    // --- Adapter/handler/step inventory for an integration ---

    public List<AdapterConfigRef> getAdapterRefs(String name, String version) {
        HIPIntegrationDefinition def = getIntegrationDefinition(name, version);
        return def != null ? def.getAdapterConfigRefs() : Collections.emptyList();
    }

    public List<HandlerConfigRef> getHandlerRefs(String name, String version) {
        HIPIntegrationDefinition def = getIntegrationDefinition(name, version);
        return def != null ? def.getHandlerConfigRefs() : Collections.emptyList();
    }

    public List<FlowStepConfigRef> getStepRefs(String name, String version) {
        HIPIntegrationDefinition def = getIntegrationDefinition(name, version);
        return def != null ? def.getFlowStepConfigRefs() : Collections.emptyList();
    }

    // --- Executor accessor (for orchestration) ---

    public TaskExecutor getFlowExecutor() {
        return flowExecutor;
    }

    // --- Topology key helper ---
    private String integrationKey(String name, String version) {
        return name + ":" + version;
    }

    // --- FlowTopology: structure for integration wiring ---
    public static class FlowTopology {
        private final HIPIntegrationDefinition definition;
        private final List<MessageChannel> channels;
        private final MessageChannel outputChannel;

        public FlowTopology(HIPIntegrationDefinition def, List<MessageChannel> channels, MessageChannel out) {
            this.definition = def;
            this.channels = Collections.unmodifiableList(new ArrayList<>(channels));
            this.outputChannel = out;
        }

        public HIPIntegrationDefinition getDefinition() { return definition; }
        public List<MessageChannel> getChannels() { return channels; }
        public MessageChannel getOutputChannel() { return outputChannel; }
    }

    // --- Channel lookup helpers for diagnostics, etc. ---
    public Map<String, MessageChannel> getChannels() {
        Map<String, MessageChannel> all = new HashMap<>();
        for (FlowTopology topo : topologyMap.values()) {
            for (MessageChannel ch : topo.getChannels()) {
                if (ch instanceof NamedComponent named) {
                    all.put(named.getComponentName(), ch);
                }
            }
        }
        return all;
    }

    public MessageChannel getChannelByName(String channelName) {
        for (FlowTopology topo : topologyMap.values()) {
            for (MessageChannel channel : topo.getChannels()) {
                if (channel instanceof NamedComponent named &&
                        channelName.equals(named.getComponentName())) {
                    return channel;
                }
            }
        }
        return null;
    }
}
