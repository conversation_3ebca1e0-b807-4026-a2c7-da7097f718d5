package com.dell.it.hip.strategy.adapters;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.server.HandlerFunction;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicHttpsAdapterConfig;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

@Component("httpsAdapter")
public class DynamicHttpsInputAdapter extends AbstractDynamicInputAdapter {

    private static final Logger logger = LoggerFactory.getLogger(DynamicHttpsInputAdapter.class);

    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;
    @Autowired
    private WiretapService wiretapService;

    // adapterKey -> handler
    private final Map<String, HandlerFunction<ServerResponse>> handlerMap = new ConcurrentHashMap<>();
    // adapterKey -> current concurrency
    private final Map<String, AtomicInteger> concurrencyMap = new ConcurrentHashMap<>();

    public Map<String, HandlerFunction<ServerResponse>> getHandlerMap() {
        return handlerMap;
    }

    @Override
    public String getType() { return "httpsAdapter"; }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        if (!getType().equals(ref.getType())) return;
        String adapterKey = key(def, ref);

        HandlerFunction<ServerResponse> handler = serverRequest -> {
            DynamicHttpsAdapterConfig cfg = (DynamicHttpsAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());

            // Set defaults
            int maxConcurrency = cfg.getMaxConcurrency() != null ? cfg.getMaxConcurrency() : 100;
            int maxRequestSizeBytes = cfg.getMaxRequestSizeBytes() != null ? cfg.getMaxRequestSizeBytes() : (2 * 1024 * 1024);
            int requestTimeoutMs = cfg.getRequestTimeoutMs() != null ? cfg.getRequestTimeoutMs() : 30000;

            // Concurrency check
            concurrencyMap.putIfAbsent(adapterKey, new AtomicInteger(0));
            AtomicInteger concurrent = concurrencyMap.get(adapterKey);
            if (concurrent.incrementAndGet() > maxConcurrency) {
                concurrent.decrementAndGet();
                logger.warn("Max concurrency exceeded for adapter {}", adapterKey);
                return ServerResponse.status(HttpStatus.TOO_MANY_REQUESTS)
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(Map.of("error", "Max concurrency exceeded"));
            }

            return serverRequest.bodyToMono(byte[].class)
                    .flatMap(body -> {
                        // Request size limit
                        if (body.length > maxRequestSizeBytes) {
                            concurrent.decrementAndGet();
                            logger.warn("Request too large for adapter {}", adapterKey);
                            return ServerResponse.status(HttpStatus.PAYLOAD_TOO_LARGE)
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .bodyValue(Map.of("error", "Request body too large"));
                        }

                        // Throttling (rate limit per second)
                        ThrottleSettings throttleSettings = getThrottleSettings(def, ref);
                        boolean allowed = (cfg.getRateLimitPerSecond() == null ||
                                (clusterCoordinationService == null) ||
                                clusterCoordinationService.isInputAllowed(def, ref, throttleSettings));
                        if (!allowed) {
                            concurrent.decrementAndGet();
                            return ServerResponse.status(HttpStatus.TOO_MANY_REQUESTS)
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .bodyValue(Map.of("error", "Too many requests"));
                        }

                        // Authentication
                        String authError = authenticate(def, ref, serverRequest);
                        if (authError != null) {
                            concurrent.decrementAndGet();
                            return ServerResponse.status(HttpStatus.UNAUTHORIZED)
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .bodyValue(Map.of("error", authError));
                        }

                        MessageChannel inputChannel = getInputChannel(def);
                        if (inputChannel == null) {
                            concurrent.decrementAndGet();
                            logger.error("No input channel for HTTPS adapter: {}", adapterKey);
                            return ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .bodyValue(Map.of("error", "No input channel configured"));
                        }

                        Message<?> msg = buildMessage(def, ref, serverRequest, body);

                        // OpenTelemetry propagation: inject trace context if not present
                        Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(msg);

                        // Wiretap: started event as soon as message is processed (not just received)
                        wiretapService.tap(msgWithTrace, def, ref, "started", "HIPIntegration received message via HTTPS");

                        TransactionLoggingUtil.sendStarted(msgWithTrace, def, "HTTPS-INBOUND");
                        inputChannel.send(msgWithTrace);

                        concurrent.decrementAndGet();
                        return ServerResponse.ok().contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(Map.of("status", "received"));
                    })
                    .timeout(java.time.Duration.ofMillis(requestTimeoutMs))
                    .onErrorResume(ex -> {
                        concurrent.decrementAndGet();
                        logger.error("HTTPS request processing failed: {}", ex.getMessage(), ex);
                        return ServerResponse.status(HttpStatus.REQUEST_TIMEOUT)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(Map.of("error", "Request timed out"));
                    });
        };

        handlerMap.put(adapterKey, handler);
        logger.info("HTTPS endpoint registered for {}", adapterKey);
    }

    private String authenticate(HIPIntegrationDefinition def, AdapterConfigRef ref, ServerRequest request) {
        DynamicHttpsAdapterConfig cfg = (DynamicHttpsAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) return "Configuration missing";

        // API key check
        if (StringUtils.hasText(cfg.getApiKeyHeader())) {
            String suppliedKey = request.headers().firstHeader(cfg.getApiKeyHeader());
            if (!Objects.equals(suppliedKey, cfg.getApiKeyValue())) {
                return "API key missing/invalid";
            }
        }
        // OAuth2
        if (cfg.isOAuthRequired()) {
            String authHeader = request.headers().firstHeader(HttpHeaders.AUTHORIZATION);
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return "OAuth2 Bearer token required";
            }
            String bearerToken = authHeader.substring(7);
            if (!StringUtils.hasText(bearerToken)) {
                return "Bearer token empty";
            }
            // Add OAuth2 validation if needed
        }
        return null;
    }

    @Override
    protected Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        return (Message<?>) raw;
    }

    private Message<?> buildMessage(
            HIPIntegrationDefinition def, AdapterConfigRef ref,
            ServerRequest request, byte[] body
    ) {
        MessageBuilder<byte[]> mb = MessageBuilder.withPayload(body);

        // OpenTelemetry context
        String traceparent = request.headers().firstHeader("traceparent");
        String baggage = request.headers().firstHeader("baggage");
        if (traceparent != null) mb.setHeader("traceparent", traceparent);
        if (baggage != null) mb.setHeader("baggage", baggage);

        // Group all HTTP/S headers
        Map<String, Object> httpsHeaders = new HashMap<>();
        Map<String, Object> finalHttpsHeaders = httpsHeaders;
        request.headers().asHttpHeaders().forEach((k, v) -> finalHttpsHeaders.put(k, v.size() == 1 ? v.get(0) : v));
        httpsHeaders.put("path", request.path());
        httpsHeaders.put("method", request.methodName());

        DynamicHttpsAdapterConfig cfg = (DynamicHttpsAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg != null && cfg.getHeadersToExtract() != null && !cfg.getHeadersToExtract().isEmpty()) {
            Map<String, Object> filtered = new HashMap<>();
            for (String key : cfg.getHeadersToExtract()) {
                if (httpsHeaders.containsKey(key)) filtered.put(key, httpsHeaders.get(key));
            }
            httpsHeaders = filtered;
        }
        mb.setHeader("hip.adapter.https", httpsHeaders);

        if (cfg != null && cfg.getAllowedHttpMethods() != null) {
            mb.setHeader("allowed_http_methods", cfg.getAllowedHttpMethods());
        }
        return mb.build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        String adapterKey = key(def, ref);
        handlerMap.remove(adapterKey);
        concurrencyMap.remove(adapterKey);
        logger.info("HTTPS handler unregistered for {}", adapterKey);
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) { /* No-op */ }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) { /* No-op */ }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        // Remove handler from map (effectively disables endpoint)
        String adapterKey = key(def, ref);
        handlerMap.remove(adapterKey);
        logger.info("HTTPS adapter paused: {}", adapterKey);
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        // Re-register the handler
        buildProducer(def, ref);
        logger.info("HTTPS adapter resumed: {}", key(def, ref));
    }
}