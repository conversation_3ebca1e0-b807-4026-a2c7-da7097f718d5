package com.dell.it.hip.strategy.handlers;

import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.util.EnumSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.keyverifier.AcceptAllServerKeyVerifier;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.keyprovider.FileKeyPairProvider;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClient.DirEntry;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicSftpHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;

@Component("sftpHandler")
public class DynamicSftpOutputHandler extends AbstractOutputHandlerStrategy {

    private final Map<String, SshClient> clientMap = new ConcurrentHashMap<>();
    private final Map<String, ClientSession> sessionMap = new ConcurrentHashMap<>();
    private final Set<String> headersToFilter;
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();

    public DynamicSftpOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "sftpHandler";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicSftpHandlerConfig config = def.getConfig(ref.getId(), DynamicSftpHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicSftpHandlerConfig not found for ref: " + ref.getId());

        String fileName = (String) message.getHeaders().get("HIP.output.FileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            wiretapService.tap(message, def, ref, "error", "Missing HIP.output.FileName header; cannot write SFTP file.");
            throw new IllegalArgumentException("Missing HIP.output.FileName header; cannot write SFTP file.");
        }

        byte[] payload = Boolean.TRUE.equals(config.getGzipEnabled())
                ? CompressionUtil.compress(message.getPayload())
                : convertToBytes(message.getPayload());

        SftpClient sftp = getOrCreateSftpClient(config, ref);
        try {
            String dir = config.getRemoteDirectory() != null ? config.getRemoteDirectory() : ".";
            boolean exists = false;
            for (DirEntry entry : sftp.readDir(dir)) {
                if (entry.getFilename().equals(fileName)) {
                    exists = true;
                    break;
                }
            }

            if (exists) {
                wiretapService.tap(message, def, ref, "error",
                        "SFTP upload aborted: File already exists: " + fileName);
                throw new RuntimeException("SFTP upload aborted: File already exists: " + fileName);
            }

            // File does not exist, write it
            String remotePath = dir.endsWith("/") ? dir + fileName : dir + "/" + fileName;
            EnumSet<SftpClient.OpenMode> openModes = EnumSet.of(SftpClient.OpenMode.Write, SftpClient.OpenMode.Create);
            try (SftpClient.CloseableHandle handle = sftp.open(remotePath, openModes)) {
                sftp.write(handle, 0L, payload, 0, payload.length);
            }
            wiretapService.tap(message, def, ref, "completed", "SFTP upload successful: " + fileName);
        } catch (Exception ex) {
            wiretapService.tap(message, def, ref, "error", "SFTP upload failed: " + ex.getMessage());
            throw ex;
        } finally {
            if (sftp != null) {
                try { sftp.close(); } catch (Exception ignored) {}
            }
        }
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    private SftpClient getOrCreateSftpClient(DynamicSftpHandlerConfig config, HandlerConfigRef ref) throws Exception {
        String key = ref.getId();

        // Reuse client per handler, or start new if not present
        SshClient client = clientMap.computeIfAbsent(key, k -> {
            SshClient c = SshClient.setUpDefaultClient();
            c.setServerKeyVerifier(AcceptAllServerKeyVerifier.INSTANCE); // For demo/lab only!
            c.start();
            return c;
        });

        // Reuse session if open, else create/authenticate
        ClientSession session = sessionMap.get(key);
        if (session == null || !session.isOpen()) {
            session = client.connect(config.getUsername(), config.getHost(), config.getPort())
                    .verify(config.getTimeout() != null ? config.getTimeout() : 10000)
                    .getSession();

            // Key-based authentication if privateKeyPath is set
            if (config.getPrivateKeyPath() != null && !config.getPrivateKeyPath().isEmpty()) {
                FileKeyPairProvider keyPairProvider = new FileKeyPairProvider(Paths.get(config.getPrivateKeyPath()));
                Iterable<KeyPair> keyPairs = keyPairProvider.loadKeys(null);
                for (KeyPair kp : keyPairs) {
                    session.addPublicKeyIdentity(kp);
                }
            }

            // Password authentication if password is set
            if (config.getPassword() != null && !config.getPassword().isEmpty()) {
                session.addPasswordIdentity(config.getPassword());
            }

            session.auth().verify(config.getTimeout() != null ? config.getTimeout() : 10000);
            sessionMap.put(key, session);
        }
        // Use SftpClientFactory to create SftpClient for the session
        return SftpClientFactory.instance().createSftpClient(session);
    }

    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getId(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = ref.getId();
        ClientSession session = sessionMap.remove(key);
        if (session != null && session.isOpen()) {
            try { session.close(); } catch (Exception ignored) {}
        }
        SshClient client = clientMap.remove(key);
        if (client != null && client.isOpen()) {
            try { client.stop(); } catch (Exception ignored) {}
        }
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        sessionMap.values().forEach(s -> { if (s.isOpen()) try { s.close(); } catch (Exception ignored) {} });
        sessionMap.clear();
        clientMap.values().forEach(c -> { if (c.isOpen()) try { c.stop(); } catch (Exception ignored) {} });
        clientMap.clear();
        handlerPauseState.clear();
        super.dispose();
    }
}