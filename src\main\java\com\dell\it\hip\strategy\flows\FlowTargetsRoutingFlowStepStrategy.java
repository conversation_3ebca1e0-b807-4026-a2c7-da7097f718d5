package com.dell.it.hip.strategy.flows;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.FlowTargetsRoutingConfig;
import com.dell.it.hip.config.FlowSteps.HandlerTarget;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.logging.WiretapService;

@Component("flowTargetsRoutingFlowStep")
public class FlowTargetsRoutingFlowStepStrategy implements FlowStepStrategy {
    private static final Logger logger = LoggerFactory.getLogger(FlowTargetsRoutingFlowStepStrategy.class);

    @Autowired
    private RuleProcessor ruleProcessor;

    @Autowired
    private ServiceManager serviceManager;
    @Autowired
    private WiretapService wiretapService;
    @Autowired
    private final ThreadPoolTaskExecutor flowExecutor;
    public FlowTargetsRoutingFlowStepStrategy(
            RuleProcessor ruleProcessor,
            ServiceManager serviceManager,
            WiretapService wiretapService,
            ThreadPoolTaskExecutor flowExecutor
    ) {
        this.ruleProcessor = ruleProcessor;
        this.serviceManager = serviceManager;
        this.wiretapService = wiretapService;
        this.flowExecutor = flowExecutor;
    }


    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        // Get rule config
        FlowTargetsRoutingConfig config = (FlowTargetsRoutingConfig) def.getConfig(stepConfigRef.getPropertyRef(), FlowTargetsRoutingConfig.class);
       // FlowTargetsRoutingConfig config = (FlowTargetsRoutingConfig) stepConfigRef.getConfig();
        List<RuleRef> ruleRefs = config.getRuleRefs();

        // --- Process rules and accumulate all handler targets (context is mutable map) ---
        Map<String, Object> context = new HashMap<>();
        Message<?> processed = ruleProcessor.processRules(message, ruleRefs, def, stepConfigRef, context);

        @SuppressWarnings("unchecked")
        List<HandlerTarget> handlerTargets = (List<HandlerTarget>) context.get("handlerTargets");

        if (handlerTargets == null || handlerTargets.isEmpty()) {
            wiretapService.tap(
                    processed,
                    def,
                    stepConfigRef,
                    "error",
                    "FlowTargetsRoutingFlowStep: No handler targets found for message, cannot route."
            );
            return List.of(); // Or optionally send to error channel if required
        }

        List<CompletableFuture<Message<?>>> futures = new ArrayList<>();
        for (HandlerTarget target : handlerTargets) {
            CompletableFuture<Message<?>> future = CompletableFuture.supplyAsync(() -> {
                return routeToHandlerTarget(processed, def, stepConfigRef, target);
            }, flowExecutor);
            futures.add(future);
        }

        List<Message<?>> results = new ArrayList<>();
        for (CompletableFuture<Message<?>> f : futures) {
            try {
                Message<?> result = f.get();
                if (result != null) {
                    results.add(result);
                }
            } catch (Exception ex) {
                logger.error("FlowTargetsRoutingFlowStep: Handler routing execution failed: {}", ex.getMessage(), ex);
            }
        }

        if (results.isEmpty()) {
            wiretapService.tap(
                    processed,
                    def,
                    stepConfigRef,
                    "error",
                    "FlowTargetsRoutingFlowStep: All handler routes failed for message."
            );
        } else {
            wiretapService.tap(
                    processed,
                    def,
                    stepConfigRef,
                    "completed",
                    "FlowTargetsRoutingFlowStep: Routed to " + results.size() + " handler(s) successfully."
            );
        }
        return results;
    }

    private Message<?> routeToHandlerTarget(
            Message<?> original,
            HIPIntegrationDefinition def,
            FlowStepConfigRef stepConfigRef,
            HandlerTarget target
    ) {
        Message<?> message = original;
        String primaryId = target.getPrimaryHandler();
        String fallbackId = target.getFallbackHandler();
        HandlerConfigRef primaryRef = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getId().equals(primaryId))
                .findFirst()
                .orElse(null);
        HandlerConfigRef fallbackRef = (fallbackId != null)
                ? def.getHandlerConfigRefs().stream().filter(r -> r.getId().equals(fallbackId)).findFirst().orElse(null)
                : null;

        HandlerStrategy primaryHandler = (primaryRef != null) ? serviceManager.getHandlerStrategy(primaryRef.getType()) : null;
        HandlerStrategy fallbackHandler = (fallbackRef != null) ? serviceManager.getHandlerStrategy(fallbackRef.getType()) : null;

        try {
            if (primaryHandler != null) {
                primaryHandler.handle(message, def, primaryRef);
                wiretapService.tap(message, def, primaryRef, "completed", "Delivered to primary handler: " + primaryId);
                return message;
            } else {
                wiretapService.tap(message, def, stepConfigRef, "warn", "Primary handler not found: " + primaryId);
            }
        } catch (Exception ex) {
            wiretapService.tap(message, def, primaryRef, "error", "Primary handler failed: " + primaryId + " ex=" + ex.getMessage());
            // Try fallback if present
            if (fallbackHandler != null) {
                try {
                    fallbackHandler.handle(message, def, fallbackRef);
                    wiretapService.tap(message, def, fallbackRef, "completed", "Delivered to fallback handler: " + fallbackId);
                    return message;
                } catch (Exception fbEx) {
                    wiretapService.tap(message, def, fallbackRef, "error", "Fallback handler failed: " + fallbackId + " ex=" + fbEx.getMessage());
                }
            }
        }

        // If neither succeeded, log and return null (or optionally send to error channel)
        wiretapService.tap(
                message,
                def,
                stepConfigRef,
                "error",
                "FlowTargetsRoutingFlowStep: Both primary and fallback handlers failed for handlerTarget [primary=" + primaryId + ", fallback=" + fallbackId + "]"
        );
        return null;
    }
    @Override
    public String getType() {
        return "FlowTargetsRouting";
    }
}