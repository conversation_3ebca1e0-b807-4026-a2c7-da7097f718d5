package com.dell.it.hip.config.FlowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;
@Data
public class MappingTransformerFlowStepConfig extends FlowStepConfig implements RuleEnabledStepConfig {

    private String transformerRef;

    private List<RuleRef> ruleRefs;

    public void setRuleRefs(List<RuleRef> ruleRefs) { this.ruleRefs = ruleRefs; }

    private boolean dbBacked;
    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }


    public String getTransformerRef() { return transformerRef; }
    public void setTransformerRef(String transformerRef) { this.transformerRef = transformerRef; }
    public boolean isDbBacked() { return dbBacked; }
    public void setDbBacked(boolean dbBacked) { this.dbBacked = dbBacked;}
}
