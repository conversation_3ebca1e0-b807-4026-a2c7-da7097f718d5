package com.dell.it.hip.strategy.adapters;

import java.util.HashMap;
import java.util.Map;

import com.dell.it.hip.util.ThrottleSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.amqp.support.converter.SimpleMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicRabbitMQAdapterConfig;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.WiretapService;

@Component("rabbitmqAdapter")
public class DynamicRabbitMQInputAdapter extends AbstractDynamicInputAdapter {

    private final Logger logger = LoggerFactory.getLogger(DynamicRabbitMQInputAdapter.class);

    @Autowired
    private CachingConnectionFactory rabbitConnectionFactory;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;

    @Override
    public String getType() {
        return "rabbitmqAdapter";
    }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        if (!getType().equals(ref.getType())) {
            return;
        }
        DynamicRabbitMQAdapterConfig cfg = (DynamicRabbitMQAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) {
            throw new IllegalStateException("No config found for RabbitMQ adapter ref: " + ref.getPropertyRef());
        }

        configureConnectionFactory(cfg);

        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(rabbitConnectionFactory);
        container.setQueueNames(cfg.getQueueName());
        if (cfg.getConcurrency() != null && cfg.getConcurrency() > 1) {
            container.setConcurrentConsumers(cfg.getConcurrency());
        }
        if (cfg.getPrefetchCount() != null) {
            container.setPrefetchCount(cfg.getPrefetchCount());
        }
        if (cfg.getAcknowledgeMode() != null) {
            container.setAcknowledgeMode(cfg.getAcknowledgeMode());
        }
        if (cfg.getChannelCacheSize() != null) {
            rabbitConnectionFactory.setChannelCacheSize(cfg.getChannelCacheSize());
        }

        MessageConverter converter = getMessageConverter(cfg);

        container.setupMessageListener((ChannelAwareMessageListener) (message, channel) -> {
            try {
                Object value = converter.fromMessage(message);
                if (cfg.isCompressed() && value instanceof byte[] bytes) {
                    value = CompressionUtil.decompress(bytes);
                }

                // Build Spring Message
                org.springframework.messaging.Message<?> inboundMsg = toMessage(def, ref, message);

                // Inject OTel context if not present
                org.springframework.messaging.Message<?> msgWithTrace =
                        openTelemetryPropagationUtil.injectTraceContext(inboundMsg);

                // --- Throttling: only allow message into HIP if within limit ---
                ThrottleSettings settings = getThrottleSettings(def, ref);
                boolean allowed = throttlingService.tryConsumeToken(
                        serviceManagerName,
                        def.getHipIntegrationName(),
                        def.getVersion(),
                        ref.getId(),
                        settings
                );
                if (!allowed) {
                    logger.info("Throttling: Not processing RabbitMQ message for queue={}, ref={}, will re-consume in next poll", cfg.getQueueName(), ref.getId());
                    // NACK (requeue) if manual ack, otherwise it will be retried per container config
                    if (container.getAcknowledgeMode().isManual()) {
                        channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                    }
                    return; // Drop for this interval, don't process now
                }

                // --- WIRETAP: started event as message enters HIP flow ---
                wiretapService.tap(
                        msgWithTrace,
                        def,
                        ref,
                        "started",
                        "HIPIntegration received message from RabbitMQ, queue=" + cfg.getQueueName()
                );

                MessageChannel inputChannel = getInputChannel(def); // Always from orchestration/serviceManager

                // Send to next stage in flow (processInboundMessage does all cluster/trace/metrics)
                processInboundMessage(def, ref, msgWithTrace, inputChannel);

                // Ack handled by container per ack mode

            } catch (Exception ex) {
                logger.error("RabbitMQ message handling error for queue={}, ref={}: {}", cfg.getQueueName(), ref.getId(), ex.getMessage(), ex);
                wiretapService.tap(null, def, ref, "error",
                        "RabbitMQ message handling error: " + ex.getMessage());
            }
        });

        MessageChannel inputChannel = getInputChannel(def); // Always from orchestration/serviceManager
        RabbitAdapterInstance instance = new RabbitAdapterInstance(container, inputChannel, cfg.getQueueName(), ref);
        registerAdapterInstance(def, ref, instance);

        container.start();
        logger.info("RabbitMQ listener started for queue={}, ref={}", cfg.getQueueName(), ref.getId());
    }

    @Override
    protected org.springframework.messaging.Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        Message rabbitMsg = (Message) raw;
        MessageProperties props = rabbitMsg.getMessageProperties();
        Object payload = rabbitMsg.getBody();

        MessageBuilder<Object> mb = MessageBuilder.withPayload(payload);

        // Extract headers as Map<String, Object>
        Map<String, Object> headerMap = new HashMap<>();
        if (props != null) {
            props.getHeaders().forEach(headerMap::put);
            if (props.getCorrelationId() != null) headerMap.put("correlationId", props.getCorrelationId());
            if (props.getMessageId() != null) headerMap.put("messageId", props.getMessageId());
            if (props.getType() != null) headerMap.put("type", props.getType());
            if (props.getAppId() != null) headerMap.put("appId", props.getAppId());
            if (props.getUserId() != null) headerMap.put("userId", props.getUserId());
        }

        promoteHeaders(headerMap, mb, def, ref);

        mb.setHeader("rabbitmq_queue", props != null ? props.getConsumerQueue() : null);
        mb.setHeader("rabbitmq_deliveryTag", props != null ? props.getDeliveryTag() : null);

        return mb.build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.stop();
            logger.info("RabbitMQ container stopped for ref={}", ref.getId());
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.start();
            logger.info("RabbitMQ container started for ref={}", rabbitInst.ref.getId());
        }
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.stop();
            logger.info("RabbitMQ container stopped for ref={}", rabbitInst.ref.getId());
        }
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            SimpleMessageListenerContainer container = rabbitInst.container;
            if (container.isRunning()) {
                container.stop();
                logger.info("RabbitMQ consumer paused for ref={}", ref.getId());
            }
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            SimpleMessageListenerContainer container = rabbitInst.container;
            if (!container.isRunning()) {
                container.start();
                logger.info("RabbitMQ consumer resumed for ref={}", ref.getId());
            }
        }
    }

    protected void configureConnectionFactory(DynamicRabbitMQAdapterConfig cfg) {
        String authType = cfg.getAuthenticationType();
        if ("TLS".equalsIgnoreCase(authType) || "CERT".equalsIgnoreCase(authType)) {
            try {
                rabbitConnectionFactory.getRabbitConnectionFactory().useSslProtocol();
            } catch (Exception e) {
                logger.error("Failed to enable TLS on RabbitMQ connection: {}", e.getMessage(), e);
                throw new IllegalStateException("RabbitMQ SSL setup failed", e);
            }
        }
        if (cfg.getUsername() != null) rabbitConnectionFactory.setUsername(cfg.getUsername());
        if (cfg.getPassword() != null) rabbitConnectionFactory.setPassword(cfg.getPassword());
        if (cfg.getVirtualHost() != null) rabbitConnectionFactory.setVirtualHost(cfg.getVirtualHost());
        if (cfg.getHost() != null) rabbitConnectionFactory.setHost(cfg.getHost());
        if (cfg.getPort() != null) rabbitConnectionFactory.setPort(cfg.getPort());
        if (cfg.getChannelCacheSize() != null) rabbitConnectionFactory.setChannelCacheSize(cfg.getChannelCacheSize());
    }

    protected MessageConverter getMessageConverter(DynamicRabbitMQAdapterConfig cfg) {
        return new SimpleMessageConverter();
    }

    public static class RabbitAdapterInstance extends AdapterInstance {
        final SimpleMessageListenerContainer container;
        final MessageChannel inputChannel;
        final String queueName;
        final AdapterConfigRef ref;

        public RabbitAdapterInstance(
                SimpleMessageListenerContainer container,
                MessageChannel inputChannel,
                String queueName,
                AdapterConfigRef ref
        ) {
            this.container = container;
            this.inputChannel = inputChannel;
            this.queueName = queueName;
            this.ref = ref;
        }
    }
}
