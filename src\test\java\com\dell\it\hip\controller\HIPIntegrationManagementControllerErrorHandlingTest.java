package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.exception.IntegrationRegistrationException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(HIPIntegrationManagementController.class)
public class HIPIntegrationManagementControllerErrorHandlingTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HIPIntegrationOrchestrationService orchestrationService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_RegistrationException_Returns400() throws Exception {
        // Arrange
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName("test-integration");
        request.setVersion("1.0");

        doThrow(new IntegrationRegistrationException("test-integration", "1.0", "Configuration invalid"))
                .when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_REGISTRATION_ERROR"))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("test-integration")))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("1.0")))
                .andExpect(jsonPath("$.correlationId").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_GenericException_Returns500() throws Exception {
        // Arrange
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName("test-integration");
        request.setVersion("1.0");

        doThrow(new RuntimeException("Unexpected database error"))
                .when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.status").value(500))
                .andExpect(jsonPath("$.errorCode").value("INTERNAL_SERVER_ERROR"))
                .andExpect(jsonPath("$.correlationId").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUnregisterIntegration_IntegrationNotFound_Returns404() throws Exception {
        // Arrange
        doThrow(new IntegrationNotFoundException("test-integration", "1.0"))
                .when(orchestrationService).unregisterHIPIntegration(anyString(), anyString());

        // Act & Assert
        mockMvc.perform(delete("/hip/management/test-integration/1.0")
                .with(csrf()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(404))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_NOT_FOUND"))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("test-integration")))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("1.0")))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUnregisterIntegration_OperationException_Returns400() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("unregister", "test-integration", "1.0", "Failed to shutdown adapters"))
                .when(orchestrationService).unregisterHIPIntegration(anyString(), anyString());

        // Act & Assert
        mockMvc.perform(delete("/hip/management/test-integration/1.0")
                .with(csrf()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_UNREGISTER_ERROR"))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("unregister")))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("test-integration")))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testPauseIntegration_IntegrationNotFound_Returns404() throws Exception {
        // Arrange
        doThrow(new IntegrationNotFoundException("test-integration", "1.0"))
                .when(orchestrationService).pauseHIPIntegration(anyString(), anyString());

        // Act & Assert
        mockMvc.perform(put("/hip/management/test-integration/1.0/pause")
                .with(csrf()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(404))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_NOT_FOUND"))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testPauseIntegration_OperationException_Returns400() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("pause", "test-integration", "1.0", "Failed to pause adapters"))
                .when(orchestrationService).pauseHIPIntegration(anyString(), anyString());

        // Act & Assert
        mockMvc.perform(put("/hip/management/test-integration/1.0/pause")
                .with(csrf()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_PAUSE_ERROR"))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("pause")))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testResumeIntegration_OperationException_Returns400() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("resume", "test-integration", "1.0", "Failed to resume handlers"))
                .when(orchestrationService).resumeHIPIntegration(anyString(), anyString());

        // Act & Assert
        mockMvc.perform(put("/hip/management/test-integration/1.0/resume")
                .with(csrf()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_RESUME_ERROR"))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("resume")))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "USER") // Not ADMIN role
    void testRegisterIntegration_AccessDenied_Returns403() throws Exception {
        // Arrange
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName("test-integration");
        request.setVersion("1.0");

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.status").value(403))
                .andExpect(jsonPath("$.errorCode").value("ACCESS_DENIED"))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_ValidationError_Returns400() throws Exception {
        // Arrange - Invalid request with missing required fields
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        // Missing required fields like hipIntegrationName, version

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"))
                .andExpect(jsonPath("$.correlationId").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_Success_Returns200() throws Exception {
        // Arrange
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName("test-integration");
        request.setVersion("1.0");

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("HIPIntegration registered"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testUnregisterIntegration_Success_Returns200() throws Exception {
        // Act & Assert
        mockMvc.perform(delete("/hip/management/test-integration/1.0")
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().string("Unregistered"));
    }
}
