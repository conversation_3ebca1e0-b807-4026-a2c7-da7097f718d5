package com.dell.it.hip.strategy.flows.rules;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
@Component
public class RuleCache {

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final Map<String, Rule> cache = new HashMap<>();

    public RuleCache(StringRedisTemplate redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    public Rule getRule(String ruleName, String ruleVersion) {
        String key = HIPRedisKeyUtil.ruleKey(ruleName, ruleVersion);
        return cache.computeIfAbsent(key, k -> loadRule(ruleName, ruleVersion));
    }

    public List<Rule> getRules(List<RuleRef> ruleRefs) {
        List<Rule> list = new ArrayList<>();
        for (RuleRef ref : ruleRefs) {
            Rule r = getRule(ref.getRuleName(), ref.getRuleVersion());
            if (r != null) list.add(r);
        }
        return list;
    }

    public Rule loadRule(String ruleName, String ruleVersion) {
        String key = HIPRedisKeyUtil.ruleKey(ruleName, ruleVersion);
        String json = redisTemplate.opsForValue().get(key);
        if (json == null) return null;
        try {
            return objectMapper.readValue(json, Rule.class);
        } catch (Exception e) {
            // log error
            return null;
        }
    }

    // Loads all rules by name/version and caches them in memory
    public void preloadRules(List<RuleRef> ruleRefs) {
        for (RuleRef ref : ruleRefs) {
            String key = HIPRedisKeyUtil.ruleKey(ref.getRuleName(), ref.getRuleVersion());
            String json = redisTemplate.opsForValue().get(key);
            if (json != null) {
                try {
                    Rule rule = objectMapper.readValue(json, Rule.class);
                    cache.put(key, rule);
                } catch (Exception ex) {
                    // log error
                }
            }
        }
    }

    public void refreshAll(List<RuleRef> ruleRefs) {
        cache.clear();
        preloadRules(ruleRefs);
    }
}