package com.dell.it.hip.config.FlowSteps;

import java.util.List;
import java.util.Objects;

/**
 * Configuration for AttributeProcessor flow step.
 */
public class AttributeProcessorConfig extends FlowStepConfig{
    private String propertyRef; // Unique key in property sheet/configMap
    private List<AttributeMapping> attributeMappings;

    public String getPropertyRef() {
        return propertyRef;
    }
    public void setPropertyRef(String propertyRef) {
        this.propertyRef = propertyRef;
    }

    public List<AttributeMapping> getAttributeMappings() {
        return attributeMappings;
    }
    public void setAttributeMappings(List<AttributeMapping> attributeMappings) {
        this.attributeMappings = attributeMappings;
    }

    @Override
    public String toString() {
        return "AttributeProcessorConfig{" +
                "propertyRef='" + propertyRef + '\'' +
                ", attributeMappings=" + attributeMappings +
                '}';
    }

    /**
     * How an attribute is derived.
     */
    public enum DerivedFromType {
        FILENAME,
        ROOT_ELEMENT,
        DERIVED_FROM_PAYLOAD
    }

}