package com.dell.it.hip.strategy.flows;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMap;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.MappingTransformerFlowStepConfig;
import com.dell.it.hip.core.registry.ContivoTransformerRegistry;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.contivoUtils.ContivoTransformerService;

public class MappingTransformerFlowStepStrategy implements FlowStepStrategy {
    private final RuleProcessor ruleProcessor;
    private final ContivoTransformerRegistry transformerRegistry;
    
    private ContivoTransformerService contivosrvice;

    public MappingTransformerFlowStepStrategy(
            RuleProcessor ruleProcessor,
            ContivoTransformerRegistry transformerRegistry
    ) {
        this.ruleProcessor = ruleProcessor;
        this.transformerRegistry = transformerRegistry;
    }

    @Override
    public String getType() {
        return "mappingTrasformer";
    }

    @Override
    public List<Message<?>> executeStep(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        // 1. Get the config for this step
        MappingTransformerFlowStepConfig stepConfig = (MappingTransformerFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), MappingTransformerFlowStepConfig.class);

        // 2. Prepare step-local context map (not carried on the message)
        Map<String, Object> context = new HashMap<>();

        // 3. Run rules, which (if SelectContivoMapRuleAction fires) will put contivoMap in context
        Message<?> messageWithMap = ruleProcessor.processRules(
                message,
                stepConfig.getRuleRefs(),
                def,
                stepConfigRef,
                context
        );

        // 4. Retrieve selected ContivoMap from context (set by SelectContivoMapRuleAction)
        ContivoMap contivoMap = (ContivoMap) context.get("contivoMap");
        if (contivoMap == null) {
            throw new RuntimeException("No ContivoMap found in context after rule processing.");
        }

        // 5. Get transformer bean and perform transformation
        ContivoTransformer transformer = transformerRegistry.get(stepConfig.getTransformerRef());
        String transformedPayload = contivosrvice.transform((String)messageWithMap.getPayload(), contivoMap);

        // 6. Build new message for downstream steps, include only lightweight map info in headers

        Message<?> outputMsg = MessageBuilder.withPayload(transformedPayload)
                .copyHeaders(messageWithMap.getHeaders())
                .setHeader("hip.contivoMapId", contivoMap.getMapIdentifier())
                .setHeader("hip.contivoMapVersion", contivoMap.getMapIdentifierVersion())
                .setHeader("hip.contivoMapName", contivoMap.getMapName())
                .build();

        // 7. Return as singleton list (pattern consistent with fan-out/aggregator steps)
        return Collections.singletonList(outputMsg);
    }
}