package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class DocTypeProcessorStepConfigTest {

    @Test
    public void testDotNotationDeserialization() throws Exception {
        // JSON with dot notation and array indices - the format we need to support
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\",\n"
                + "  \"supportedDocTypesPerFormat.XML[0]\": \"xmldoc:1\",\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        
        // Check JSON format mapping
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        
        // Check XML format mapping
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("XML"));
        assertEquals(1, config.getSupportedDocTypesPerFormat().get("XML").size());
        assertEquals("xmldoc:1", config.getSupportedDocTypesPerFormat().get("XML").get(0));
        
        // Check other properties
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());
        
        System.out.println("✅ Successfully deserialized dot notation JSON!");
        System.out.println("Supported doc types for JSON: " + config.getSupportedDocTypesPerFormat().get("JSON"));
        System.out.println("Supported doc types for XML: " + config.getSupportedDocTypesPerFormat().get("XML"));
    }

    @Test
    public void testOriginalHIPIntegrationMapperJson() throws Exception {
        // This is the exact JSON from HIPIntegrationMapper.java
        String json = "{\r\n"
                + "        \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\r\n"
                + "        \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\"\r\n"
                + "      }";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization - this should now work without UnrecognizedPropertyException
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        
        System.out.println("✅ HIPIntegrationMapper JSON format works!");
        System.out.println("Config: " + config);
    }
}
