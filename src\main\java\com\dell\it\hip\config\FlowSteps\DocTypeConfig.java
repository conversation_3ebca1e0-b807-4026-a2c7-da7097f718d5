package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class DocTypeConfig {
    private String name;
    private String version;
    private DocTypeRuleOperator docTypeRuleOperator = DocTypeRuleOperator.ALL; // ALL or ANY
    private List<DocTypeIdentifier> docTypeIdentifiers;

    private ValidationConfig validation;
    @JsonProperty("attributeMappings")
    private Map<String, AttributeMapping> attributeMappings = new HashMap<>();

    @JsonSetter("attributeMappings")
    public void setAttributeMappings(Object attributeMappings) {
        if (attributeMappings instanceof Map) {
            this.attributeMappings = (Map<String, AttributeMapping>) attributeMappings;
        } else if (attributeMappings instanceof List) {
            List<AttributeMapping> list = (List<AttributeMapping>) attributeMappings;
            this.attributeMappings = list.stream()
                    .collect(Collectors.toMap(AttributeMapping::getAttributeName, Function.identity()));
        }
    }
    public Map<String, AttributeMapping> getAttributeMappings() { return attributeMappings; }
    // Getters & Setters
    // ...
}