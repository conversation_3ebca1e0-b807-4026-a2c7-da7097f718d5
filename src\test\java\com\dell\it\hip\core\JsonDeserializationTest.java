package com.dell.it.hip.core;

import com.dell.it.hip.config.FlowSteps.DocTypeProcessorStepConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class JsonDeserializationTest {

    @Test
    public void testDocTypeProcessorStepConfigDeserialization() throws Exception {
        // JSON string with correct structure for DocTypeProcessorStepConfig
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat\": {\n"
                + "    \"JSON\": [\"testdoc:1\", \"testdoce1:1\"]\n"
                + "  },\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());
        
        System.out.println("Successfully deserialized config: " + config);
        System.out.println("Supported doc types for JSON: " + config.getSupportedDocTypesPerFormat().get("JSON"));
    }

    @Test
    public void testOriginalProblematicJson() throws Exception {
        // This is the original problematic JSON structure that should fail
        String problematicJson = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // This should throw an UnrecognizedPropertyException
        assertThrows(Exception.class, () -> {
            objectMapper.readValue(problematicJson, DocTypeProcessorStepConfig.class);
        });
    }
}
